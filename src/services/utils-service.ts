import { Timestamp } from 'firebase/firestore';
import { AppAuthor, AppResourceType, AppTag, AppResource } from '../core.types';

const WELCOME_MODAL_STATE_LOCAL_STORAGE_KEY = 'welcome-modal-state';
const WELCOME_MODAL_SHOWN_STATE_VALUE = 'shown';

const isMobile = window.innerWidth <= 1024;

export const enumToArray = (e: any) => Object.keys(e).map((k) => e[k]);

export const formatDateToFirebaseTimestamp = (date?: string | Date) => {
  if (date instanceof Date) {
    return Timestamp.fromDate(date);
  }

  return Timestamp.fromDate(date ? new Date(date) : new Date());
};

export const firebaseTimestampToString = (timestamp: Timestamp) => {
  return timestamp.toDate().toISOString();
};

export const firebaseTimestampToFriendlyDate = (timestamp: Timestamp) => {
  const date = timestamp.toDate();
  return date.toLocaleDateString('default', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

export const setTagsUpdateIdInLocalStorage = (id: string) => {
  try {
    localStorage.setItem('tagsUpdateId', id);
  } catch (e) {
    console.log(e);
  }
};

export const getTagsUpdateIdFromLocalStorage = () => {
  try {
    return localStorage.getItem('tagsUpdateId');
  } catch (e) {
    console.log(e);
  }
};

export const getAuthorsUpdateIdFromLocalStorage = () => {
  try {
    return localStorage.getItem('authorsUpdateId');
  } catch (e) {
    console.log(e);
  }
};

export const setAuthorsUpdateIdInLocalStorage = (id: string) => {
  try {
    localStorage.setItem('authorsUpdateId', id);
  } catch (e) {
    console.log(e);
  }
};

export const setTagsInLocalStorage = (tags: AppTag[]) => {
  try {
    localStorage.setItem('tags', JSON.stringify(tags));
  } catch (e) {
    console.log(e);
  }
};

export const getTagsFromLocalStorage = (): AppTag[] => {
  try {
    const tags = localStorage.getItem('tags');
    return tags ? JSON.parse(tags) : [];
  } catch (e) {
    console.log(e);
    return [];
  }
};

export const setAuthorsInLocalStorage = (authors: AppAuthor[]) => {
  if (localStorage.getItem('bloggers') !== null) {
    localStorage.removeItem('bloggers');
  }

  try {
    localStorage.setItem('authors', JSON.stringify(authors));
  } catch (e) {
    console.log(e);
  }
};

export const getAuthorsFromLocalStorage = (): AppAuthor[] => {
  try {
    const authors = localStorage.getItem('authors');
    return authors ? JSON.parse(authors) : [];
  } catch (e) {
    console.log(e);
    return [];
  }
};

export const setInitialWelcomeModalShownInLocalStorage = () => {
  try {
    localStorage.setItem(
      WELCOME_MODAL_STATE_LOCAL_STORAGE_KEY,
      WELCOME_MODAL_SHOWN_STATE_VALUE,
    );
  } catch (e) {
    console.log(e);
  }
};

export const isInitialWelcomeModalShown = () => {
  try {
    const value = localStorage.getItem(WELCOME_MODAL_STATE_LOCAL_STORAGE_KEY);
    return value === WELCOME_MODAL_SHOWN_STATE_VALUE;
  } catch (e) {
    return false;
  }
};

export const getClickHandler = (handler) =>
  isMobile ? { onClick: handler } : { onTouchEnd: handler };

export const getUserNameRepoFromUrl = (url: string) => {
  const urlParts = url.split('/');
  return {
    userName: urlParts[urlParts.length - 2],
    repoName: urlParts[urlParts.length - 1],
  };
};

export const groupTags = (tags) => {
  return tags.reduce((acc, tag) => {
    acc[tag.group] = acc[tag.group] || [];
    acc[tag.group].push(tag);
    return acc;
  }, {});
};

export const filterResourceByType = (
  appResources: AppResource[],
  resourceType: AppResourceType,
) => appResources.filter((appResource) => appResource.type === resourceType);
