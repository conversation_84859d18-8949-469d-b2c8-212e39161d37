import { cn } from '@/lib/utils';
import { AppResource } from '@/core.types';
import { LikeAction } from '@/search-page/resource-actions/like-action';
import { DislikeAction } from '@/search-page/resource-actions/dislike-action';
import { BookmarkAction } from '@/search-page/resource-actions/bookmark-action';
import { ReadMarkAction } from '@/search-page/resource-actions/readmark-action';
import { EditAction } from '@/search-page/resource-actions/edit-action';
import { DeleteAction } from '@/search-page/resource-actions/delete-action';
import { useRootContext } from '@/context/useRootContext';

interface CardActionsProps {
  resource: AppResource;
  variant?: 'floating' | 'inline';
  showEditDelete?: boolean;
  className?: string;
}

export const CardActions = ({
  resource,
  variant = 'floating',
  showEditDelete = false,
  className,
}: CardActionsProps) => {
  const { isAuthorized } = useRootContext();
  // Do not show any actions if user is not authenticated
  if (!isAuthorized) return null;

  // Small helper class for circular action wrappers
  const actionCircle = 'h-8 w-8 rounded-full flex items-center justify-center';

  if (variant === 'floating') {
    return (
      <div
        className={cn(
          'absolute -top-2 right-2 flex gap-1 p-1 rounded-full bg-background/80 backdrop-blur-sm border border-border/40',
          'md:opacity-0 md:group-hover:opacity-100 transition-opacity duration-200',
          'opacity-100', // Always visible on mobile
          className,
        )}
      >
        <LikeAction resource={resource} />
        <DislikeAction resource={resource} />
        <BookmarkAction appResourceId={resource.id} />
        <ReadMarkAction appResourceId={resource.id} />
        {showEditDelete && (
          <>
            <div className={actionCircle}>
              <EditAction resource={resource} />
            </div>
            <div className={actionCircle}>
              <DeleteAction resourceId={resource.id} />
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <LikeAction resource={resource} />
      <DislikeAction resource={resource} />
      <BookmarkAction appResourceId={resource.id} />
      <ReadMarkAction appResourceId={resource.id} />
      {showEditDelete && (
        <>
          <EditAction resource={resource} />
          <DeleteAction resourceId={resource.id} />
        </>
      )}
    </div>
  );
};
