import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useRootContext } from '@/context/useRootContext';
import { CircleUserIcon, Plus } from 'lucide-react';
import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import logo from '../../icons/logo.png';
import { AppTheme } from '../AppTheme';
import { AppButtonSecondary } from '../buttons/app-button-new';
import { ProposalModal } from '../proposal-modal';

import { WelcomeModal } from '../welcome-modal';
import { ShareButton } from './share-button';
import { AuthModalDrawer } from '../auth/auth-modal-drawer';
import { MobileFilterDrawer } from '../mobile/mobile-filter-drawer';

export const AppHeader = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const navigateTo = useCallback(
    (path: string) => {
      if (pathname === path) return;
      navigate(path);
    },
    [navigate, pathname],
  );

  const { isAuthorized, logout, hasAdminRole } = useRootContext();

  const onLogoClick = () => {
    navigate('/');
  };

  const addResourceButton = (
    <AppButtonSecondary>
      <Plus className='cursor-pointer' strokeWidth={2} />
    </AppButtonSecondary>
  );

  return (
    <header className='flex h-16 items-center gap-4 border-b bg-background px-4 md:px-2'>
      <nav className='font-medium flex items-center gap-3 text-sm flex-grow'>
        <div
          onClick={onLogoClick}
          className='cursor-pointer w-[50px] h-[50px] rounded-sm backdrop-blur-sm bg-white drop-shadow-lg'
        >
          <img src={logo} alt='' />
        </div>
        <WelcomeModal />
      </nav>

      <div className='flex gap-2 items-center md:hidden'>
        <MobileFilterDrawer />
        <AppTheme />
        <ShareButton />

        {!isAuthorized && <AuthModalDrawer />}
        {isAuthorized && <ProposalModal trigger={addResourceButton} />}
        {isAuthorized && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='secondary' size='icon' className='rounded-full'>
                <CircleUserIcon className='h-4 w-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
              {hasAdminRole && (
                <DropdownMenuItem onClick={() => navigateTo('/admin')}>
                  Admin
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Desktop header controls */}
      <div className='ml-auto md:flex gap-2 items-center hidden'>
        <AppTheme />
        <ShareButton />
        {isAuthorized && <ProposalModal trigger={addResourceButton} />}
        {!isAuthorized && <AuthModalDrawer />}
        {isAuthorized && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant='secondary'
                size='icon'
                className='rounded-full ml-auto'
              >
                <CircleUserIcon className='h-5 w-5' />
              </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align='end'>
              {hasAdminRole && (
                <DropdownMenuItem onClick={() => navigateTo('/admin')}>
                  Admin
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
};
