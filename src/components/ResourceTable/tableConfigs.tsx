import React from 'react';
import { Badge } from '@/components/ui/badge';
import { AppResource, AppResourceType } from '@/core.types';
import { AppAuthorNameAvatar } from '@/components/AppAuthorNameAvatar/AppAuthorNameAvatar';
import { LikeAction } from '@/search-page/resource-actions/like-action';
import { DislikeAction } from '@/search-page/resource-actions/dislike-action';
import { BookmarkAction } from '@/search-page/resource-actions/bookmark-action';
import { ReadMarkAction } from '@/search-page/resource-actions/readmark-action';
import { firebaseTimestampToFriendlyDate } from '@/services/utils-service';
import { useRootContext } from '@/context/useRootContext';
import { ResourceTableColumn } from './ResourceTable';
import {
  ExternalLink,
  Github,
  Video,
  FileText,
  Twitter,
  Globe,
} from 'lucide-react';

// Author cell component that uses the context
const AuthorCell = ({ resource }: { resource: AppResource }) => {
  const { authors } = useRootContext();
  const author = authors.find((a) => a.id === resource.authorId);
  return (
    <AppAuthorNameAvatar
      author={author}
      nameLength={100}
      customTwitterId={resource.customTwitterId}
    />
  );
};

// Tags cell component that uses the context to get tag names
const TagsCell = ({ resource }: { resource: AppResource }) => {
  const { tags } = useRootContext();
  const resourceTags = resource.tags
    .map((tagId) => tags.find((tag) => tag.id === tagId))
    .filter(Boolean);

  return (
    <div className='flex flex-wrap gap-1'>
      {resourceTags.slice(0, 3).map((tag) => (
        <Badge key={tag!.id} variant='outline' className='text-xs'>
          {tag!.name}
        </Badge>
      ))}
      {resourceTags.length > 3 && (
        <Badge variant='outline' className='text-xs'>
          +{resourceTags.length - 3}
        </Badge>
      )}
    </div>
  );
};

// Resource type icon component
const ResourceTypeIcon = ({ type }: { type: AppResourceType }) => {
  const iconProps = { className: 'h-4 w-4 mr-2' };

  switch (type) {
    case AppResourceType.Article:
      return <FileText {...iconProps} />;
    case AppResourceType.Video:
      return <Video {...iconProps} />;
    case AppResourceType.Tweet:
      return <Twitter {...iconProps} />;
    case AppResourceType.Github:
      return <Github {...iconProps} />;
    case AppResourceType.Sites:
      return <Globe {...iconProps} />;
    case AppResourceType.Papers:
      return <FileText {...iconProps} />;
    default:
      return <ExternalLink {...iconProps} />;
  }
};

// Common columns that can be reused
export const commonColumns = {
  title: {
    key: 'title',
    label: 'Title',
    sortable: true,
    render: (resource: AppResource) => (
      <div className='max-w-md'>
        <a
          href={resource.url}
          target='_blank'
          rel='noopener noreferrer'
          className='text-foreground hover:text-primary underline font-medium flex items-center mb-2'
        >
          <ResourceTypeIcon type={resource.type} />
          {resource.title}
        </a>
        {resource.description && (
          <p className='text-sm text-muted-foreground mb-2 line-clamp-2'>
            {resource.description}
          </p>
        )}
        <TagsCell resource={resource} />
      </div>
    ),
  },

  author: {
    key: 'author',
    label: 'Author',
    render: (resource: AppResource) => <AuthorCell resource={resource} />,
  },

  type: {
    key: 'type',
    label: 'Type',
    sortable: true,
    render: (resource: AppResource) => (
      <Badge variant='secondary' className='capitalize flex items-center w-fit'>
        <ResourceTypeIcon type={resource.type} />
        {resource.type}
      </Badge>
    ),
  },

  tags: {
    key: 'tags',
    label: 'Tags',
    render: (resource: AppResource) => (
      <div className='flex flex-wrap gap-1 max-w-xs'>
        {resource.tags.slice(0, 3).map((tag) => (
          <Badge key={tag} variant='outline' className='text-xs'>
            {tag}
          </Badge>
        ))}
        {resource.tags.length > 3 && (
          <Badge variant='outline' className='text-xs'>
            +{resource.tags.length - 3}
          </Badge>
        )}
      </div>
    ),
  },

  engagement: {
    key: 'engagement',
    label: 'Engagement',
    render: (resource: AppResource) => (
      <div className='flex items-center gap-2'>
        <span className='text-sm text-muted-foreground'>
          👍 {resource.likesCount || 0}
        </span>
        <span className='text-sm text-muted-foreground'>
          👎 {resource.dislikesCount || 0}
        </span>
      </div>
    ),
  },

  // createdAt: {
  //   key: 'createdAt',
  //   label: 'Created',
  //   sortable: true,
  //   render: (resource: AppResource) => (
  //     <span className='text-sm text-muted-foreground'>
  //       {firebaseTimestampToFriendlyDate(resource.createdAt as any)}
  //     </span>
  //   ),
  // },

  actions: {
    key: 'actions',
    label: 'Actions',
    render: (resource: AppResource) => (
      <div className='flex items-center gap-1'>
        <LikeAction resource={resource} />
        <DislikeAction resource={resource} />
        <BookmarkAction appResourceId={resource.id} />
        <ReadMarkAction appResourceId={resource.id} />
      </div>
    ),
  },
} satisfies Record<string, ResourceTableColumn>;

// Default configuration for all resource types
export const defaultTableConfig: ResourceTableColumn[] = [
  commonColumns.title,
  commonColumns.author,
  commonColumns.type,
  commonColumns.tags,
  commonColumns.engagement,
  commonColumns.createdAt,
  commonColumns.actions,
];

// Compact configuration for smaller screens or when space is limited
export const compactTableConfig: ResourceTableColumn[] = [
  commonColumns.title,
  commonColumns.author,
  commonColumns.type,
  commonColumns.actions,
];

// Configuration specific to articles and papers
export const articleTableConfig: ResourceTableColumn[] = [
  commonColumns.title,
  commonColumns.author,
  commonColumns.tags,
  commonColumns.engagement,
  commonColumns.createdAt,
  commonColumns.actions,
];

// Configuration specific to videos
export const videoTableConfig: ResourceTableColumn[] = [
  commonColumns.title,
  commonColumns.author,
  {
    key: 'duration',
    label: 'Duration',
    render: (resource: AppResource) => (
      <span className='text-sm text-muted-foreground'>
        {/* You can extract duration from video URL or metadata if available */}
        Video
      </span>
    ),
  },
  commonColumns.tags,
  commonColumns.engagement,
  commonColumns.createdAt,
  commonColumns.actions,
];

// Configuration specific to GitHub repositories
export const githubTableConfig: ResourceTableColumn[] = [
  commonColumns.title,
  commonColumns.author,
  {
    key: 'repository',
    label: 'Repository',
    render: (resource: AppResource) => {
      const urlParts = resource.url.split('/');
      const repoName = urlParts[urlParts.length - 1];
      const userName = urlParts[urlParts.length - 2];
      return (
        <div className='text-sm'>
          <span className='font-mono text-muted-foreground'>
            {userName}/{repoName}
          </span>
        </div>
      );
    },
  },
  commonColumns.tags,
  commonColumns.engagement,
  commonColumns.createdAt,
  commonColumns.actions,
];

// Configuration specific to tweets
export const tweetTableConfig: ResourceTableColumn[] = [
  {
    key: 'tweet',
    label: 'Tweet',
    render: (resource: AppResource) => (
      <div className='max-w-sm'>
        <a
          href={resource.url}
          target='_blank'
          rel='noopener noreferrer'
          className='text-blue-600 hover:text-blue-800 underline font-medium flex items-center'
        >
          <Twitter className='h-4 w-4 mr-2' />
          {resource.title}
        </a>
        {resource.description && (
          <p className='text-sm text-muted-foreground mt-1 line-clamp-3'>
            {resource.description}
          </p>
        )}
      </div>
    ),
  },
  commonColumns.author,
  commonColumns.tags,
  commonColumns.engagement,
  commonColumns.createdAt,
  commonColumns.actions,
];

// Function to get table configuration based on resource type
export const getTableConfigForResourceType = (
  resourceType?: AppResourceType,
): ResourceTableColumn[] => {
  switch (resourceType) {
    case AppResourceType.Article:
    case AppResourceType.Papers:
      return articleTableConfig;
    case AppResourceType.Video:
      return videoTableConfig;
    case AppResourceType.Github:
      return githubTableConfig;
    case AppResourceType.Tweet:
      return tweetTableConfig;
    default:
      return defaultTableConfig;
  }
};
