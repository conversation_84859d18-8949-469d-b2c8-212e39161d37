import { useState } from 'react';
import { FaGoogle } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';
import { LogIn } from 'lucide-react';
import { Drawer } from 'vaul';
import { signInWithGoogle, signInWithTwitter } from '../../api';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

interface AuthModalDrawerProps {
  className?: string;
}

const AuthButtons = () => {
  return (
    <div className='flex flex-col gap-4 p-6'>
      <div className='text-center mb-4'>
        <h3 className='text-lg font-semibold mb-2'>Sign in to SmartSecHub</h3>
        <p className='text-sm text-muted-foreground'>
          Choose your preferred sign-in method
        </p>
      </div>

      <Button
        onClick={signInWithGoogle}
        variant='outline'
        className='w-full h-12 text-base font-medium border-2 hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-950 dark:hover:border-blue-800 transition-colors'
      >
        <FaGoogle className='mr-3 h-5 w-5 text-blue-600' />
        Sign in with Google
      </Button>

      <Button
        onClick={signInWithTwitter}
        variant='outline'
        className='w-full h-12 text-base font-medium border-2 hover:bg-slate-50 hover:border-slate-200 dark:hover:bg-slate-950 dark:hover:border-slate-800 transition-colors'
      >
        <FaXTwitter className='mr-3 h-5 w-5 text-slate-900 dark:text-slate-100' />
        Sign in with Twitter
      </Button>
    </div>
  );
};

export const AuthModalDrawer = ({ className }: AuthModalDrawerProps) => {
  const [open, setOpen] = useState(false);
  const isMobile = typeof window !== 'undefined' && window.innerWidth <= 768;

  const triggerButton = (
    <Button
      variant='outline'
      className={cn(
        'h-10 px-4 font-medium border-2 hover:bg-primary/10 hover:border-primary/20 transition-colors',
        'border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-200',
        'hover:border-yellow-500 hover:text-yellow-600 dark:hover:text-yellow-400',
        className,
      )}
    >
      <LogIn className='mr-2 h-4 w-4' />
      Sign In
    </Button>
  );

  if (isMobile) {
    return (
      <Drawer.Root open={open} onOpenChange={setOpen}>
        <Drawer.Trigger asChild>{triggerButton}</Drawer.Trigger>
        <Drawer.Portal>
          <Drawer.Overlay className='fixed inset-0 bg-black/40 ' />
          <Drawer.Content className='bg-background z-[100] flex flex-col rounded-t-[10px] h-fit fixed bottom-0 left-0 right-0 outline-none'>
            <div className='p-4 bg-background rounded-t-[10px] flex-1'>
              <div className='mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-muted mb-8' />
              <AuthButtons />
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
    );
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{triggerButton}</DialogTrigger>
      <DialogContent className='sm:max-w-md'>
        <DialogHeader>
          <DialogTitle className='sr-only'>Authentication</DialogTitle>
        </DialogHeader>
        <AuthButtons />
      </DialogContent>
    </Dialog>
  );
};
