import { groupTags } from '@/services/utils-service';
import { useCallback } from 'react';
import { toast } from 'sonner';
import { createTag } from '../../api';
import { useRootContext } from '../../context/useRootContext';
import { useAdminCounters } from '../../context/AdminCountersContext';
import { AppTag, TagGroupName } from '../../core.types';
import { ManageTagForm } from './ManageTagForm';
import { ManageTagsItem } from './ManageTagsItem';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tags } from 'lucide-react';

export const ManageTags = () => {
  const { tags, setTags } = useRootContext();
  const { refreshCounters } = useAdminCounters();

  const onTagCreate = useCallback(
    async ({ group, name }: { group: any; name: string }) => {
      const isAlreadyAdded = tags.find((tag) => tag.name === name);
      if (isAlreadyAdded) {
        toast.error('Tag is already added');
        return;
      }

      const tagId = await createTag({ group, name } as any);
      if (tagId) {
        setTags((prevTags: AppTag[]) => [
          ...prevTags,
          { group, name, id: tagId },
        ]);
        // Refresh counters after successful creation
        refreshCounters();
      }
    },
    [setTags, tags, refreshCounters],
  );

  const groupedTags = groupTags(tags);

  return (
    <div className='p-4 space-y-4'>
      <div className='grid grid-cols-1 lg:grid-cols-4 gap-4'>
        <div className='lg:col-span-1'>
          <Card>
            <CardHeader className='pb-3'>
              <CardTitle className='text-lg flex items-center gap-2 text-white'>
                <Tags className='w-4 h-4' />
                Add Tag
              </CardTitle>
            </CardHeader>
            <CardContent className='pt-0'>
              <ManageTagForm onSubmit={onTagCreate} />
            </CardContent>
          </Card>
        </div>

        {/* Tags by Category */}
        <div className='lg:col-span-3'>
          <div className='grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4'>
            {Object.entries(groupedTags).map(([group, tagsInGroup]) => (
              <Card key={group}>
                <CardHeader className='pb-3'>
                  <CardTitle className='text-base text-white'>
                    {TagGroupName[group]} ({(tagsInGroup as AppTag[]).length})
                  </CardTitle>
                </CardHeader>
                <CardContent className='pt-0'>
                  <div className='flex flex-wrap gap-2'>
                    {(tagsInGroup as AppTag[]).map((tag) => (
                      <ManageTagsItem key={tag.id} tag={tag} />
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
