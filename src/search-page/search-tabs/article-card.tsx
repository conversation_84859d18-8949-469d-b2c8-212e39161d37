import { AppAuthorNameAvatar } from '@/components/AppAuthorNameAvatar';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import { useRootContext } from '@/context/useRootContext';
import { AppResource, AppTag } from '@/core.types';
import { firebaseTimestampToFriendlyDate } from '@/services/utils-service';
import { cn } from '@/utils';
import { ResourceTags } from './resource-tags';
import { CardActions } from '@/components/CardActions/CardActions';

export const ArticleCard = ({ resource }: { resource: AppResource }) => {
  const { tags, authors, hasAdminRole } = useRootContext();

  const author = authors.find((author) => author.id === resource.authorId);
  const tagsList = resource.tags
    .map((tagId) => tags.find((tag) => tag.id === tagId))
    .filter(Boolean) as AppTag[];

  return (
    <div
      key={resource.id}
      className='flex flex-col items-start gap-2 rounded-lg border p-3 text-left text-sm transition-all hover:bg-accent max-h-[400px] relative group'
    >
      <GlowingEffect
        disabled={false}
        spread={40}
        glow={true}
        proximity={64}
        inactiveZone={0.01}
        borderWidth={1.5}
      />
      <CardActions
        resource={resource}
        variant='floating'
        showEditDelete={hasAdminRole}
      />
      <div className='w-full'>
        <div className='flex gap-2 justify-between w-full items-center mb-2 flex-wrap'>
          <AppAuthorNameAvatar
            author={author}
            nameLength={130}
            customTwitterId={resource.customTwitterId}
          />
        </div>

        <div className='flex items-start justify-between'>
          <div className='flex items-center gap-2'>
            <a
              href={resource.url}
              target='_blank'
              rel='noopener noreferrer'
              className='font-semibold hover:underline cursor-pointer'
            >
              {resource.title}
            </a>
          </div>
          {/* <div
            className={cn(
              'ml-auto text-xs text-right mr-3',
              'text-muted-foreground min-w-[100px]',
            )}
          >
            {firebaseTimestampToFriendlyDate(resource.createdAt)}
          </div> */}
        </div>
      </div>
      <ResourceTags tags={tagsList} />
      <div className='flex justify-end gap-2 py-2 w-full'></div>
    </div>
  );
};
