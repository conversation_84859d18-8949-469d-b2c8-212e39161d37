import { AppAuthorNameAvatar } from '@/components/AppAuthorNameAvatar';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import { useRootContext } from '../../context/useRootContext';
import { AppResource, AppTag } from '../../core.types';

import { ResourceTags } from './resource-tags';

import { CardActions } from '@/components/CardActions/CardActions';
interface VideoTileProps {
  resource: AppResource;
}

export const VideoCard = ({ resource }: VideoTileProps) => {
  const embedUrl = resource.url.replace('watch?v=', 'embed/');
  const { authors, tags, hasAdminRole } = useRootContext();

  const author = authors.find((author) => author.id === resource.authorId);

  const tagsList = resource.tags.map((tagId) =>
    tags.find((tag) => tag.id === tagId),
  ) as AppTag[];

  return (
    <Card className='flex flex-col cursor-pointer relative group'>
      <GlowingEffect
        disabled={false}
        spread={40}
        glow={true}
        proximity={64}
        inactiveZone={0.01}
        borderWidth={1.5}
      />
      <CardActions
        resource={resource}
        variant='floating'
        showEditDelete={hasAdminRole}
      />
      {/* <div className='flex justify-end f-full pt-2'>
        <span className='font-semibold whitespace-pre-wrap text-black dark:text-[#d4d1d1] flex gap-2 items-center text-[12px] mr-auto ml-6'>
          {firebaseTimestampToFriendlyDate(resource?.createdAt as any)}
        </span>
      </div> */}
      <div className='py-4 pl-6'>
        <AppAuthorNameAvatar
          author={author}
          nameLength={130}
          customTwitterId={resource.customTwitterId}
        />
      </div>

      <CardContent>
        <div className='min-w-[300px] grow'>
          <h1 className='text-lg'>{resource.title}</h1>
          <iframe
            src={embedUrl}
            title='YouTube video'
            allowFullScreen
            className='w-full h-full min-h-[300px] py-3'
          />
          <div className='mt-2 text-[13px] text-right'>
            Have problem with this video? Click{' '}
            <a href={resource.url} target='_blank' className='underline'>
              here
            </a>
          </div>
        </div>
      </CardContent>
      <CardFooter className='grow'>
        <ResourceTags tags={tagsList} className='mt-auto' />
      </CardFooter>
    </Card>
  );
};
