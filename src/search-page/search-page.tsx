import { App<PERSON>eader } from '@/components/app-header/app-header';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import { Separator } from '@/components/ui/separator';

import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TooltipProvider } from '@/components/ui/tooltip';
import { useRootContext } from '@/context/useRootContext';
import { AppResourceType } from '@/core.types';

import { CSSProperties, useState } from 'react';
import { AppAuthorSelect } from 'src/components/AppSelect/AppAuthorSelect';
import { cn } from 'src/utils';
import { useAppResourcesContext } from './app-resources-context';
import { useSearchFiltersContext } from './search-filters-context';
import { ArticleTab } from './search-tabs/article-tab';
import { GithubTab } from './search-tabs/github-tab';
import { ProposalsTab } from './search-tabs/proposals-tab';
import { SitesTab } from './search-tabs/sites-tab';
import { TweetsTab } from './search-tabs/tweets-tab';
import { VideoTab } from './search-tabs/videos-tab';
import { SearchTags } from './search-tags';
import { GlobalViewProvider, useGlobalView } from '@/context/GlobalViewContext';
import { ViewSwitcher } from '@/components/ViewSwitcher';
import { AppMetadata } from '@/components/AppMetadata';
import { BookmarksToggle } from '@/components/BookmarksToggle';
import { MobileCategoryPill } from '@/components/mobile/mobile-category-pill';

const defaultLayout = [225, 440 + 655];

// Component that uses global view context
function GlobalViewSwitcher({ currentTab }: { currentTab: string }) {
  const { globalViewMode, setGlobalViewMode } = useGlobalView();

  // Hide view switcher for GitHub, Tweets, and Proposals tabs
  const hiddenTabs = [
    AppResourceType.Github,
    AppResourceType.Tweet,
    'proposal',
  ];
  if (
    hiddenTabs.includes(currentTab as AppResourceType) ||
    currentTab === 'proposal'
  ) {
    return null;
  }

  return (
    <div className='flex items-center gap-2'>
      <ViewSwitcher
        currentView={globalViewMode}
        onViewChange={setGlobalViewMode}
        className='scale-90'
      />
    </div>
  );
}

function SearchPageContent() {
  const { authorId, setAuthorID, resourceType, setResourceType } =
    useSearchFiltersContext();
  const { isAuthorized } = useRootContext();
  const {
    resetAndReloadResources,
    bookmarksMode,
    showBookMarks,
    hideBookMarks,
  } = useAppResourcesContext();

  // Track current tab including proposal tab
  const [currentTab, setCurrentTab] = useState<string>(resourceType);

  // Dynamic page title based on current tab and bookmarks mode
  const getPageTitle = () => {
    const baseTitle = bookmarksMode ? 'My Bookmarks' : 'Search Resources';
    const typeMap = {
      [AppResourceType.Article]: 'Articles',
      [AppResourceType.Video]: 'Videos',
      [AppResourceType.Tweet]: 'Tweets',
      [AppResourceType.Github]: 'GitHub Repos',
      [AppResourceType.Sites]: 'Sites',
      [AppResourceType.Papers]: 'Research Papers',
    };
    const currentType = typeMap[resourceType] || 'Resources';
    return `${currentType} - ${baseTitle}`;
  };

  const getPageDescription = () => {
    if (bookmarksMode) {
      return 'Browse your bookmarked smart contract security resources and research materials.';
    }
    const typeDescriptions = {
      [AppResourceType.Article]:
        'Discover the latest articles and blog posts about smart contract security, auditing, and best practices.',
      [AppResourceType.Video]:
        'Watch educational videos, tutorials, and conference talks about blockchain security.',
      [AppResourceType.Tweet]:
        'Follow the latest discussions and insights from security experts on Twitter.',
      [AppResourceType.Github]:
        'Explore open-source security tools, libraries, and research repositories.',
      [AppResourceType.Sites]:
        'Find useful websites, platforms, and resources for smart contract security.',
      [AppResourceType.Papers]:
        'Read academic papers and research publications on blockchain security.',
    };
    return (
      typeDescriptions[resourceType] ||
      'Search and discover smart contract security resources from top experts and researchers.'
    );
  };

  const onAuthorSelectChange = (authorId: string) => {
    setAuthorID(authorId);
    resetAndReloadResources();
  };

  const onResourceTypeChange = (resourceType: AppResourceType) => {
    setResourceType(resourceType);
    if (!bookmarksMode) {
      resetAndReloadResources();
    }
  };

  const onBookmarkSwitchChange = (value: boolean) => {
    if (value) {
      showBookMarks();
    } else {
      hideBookMarks();
    }
  };

  const isMobile = window.innerWidth <= 768;

  const TabContentStyle = isMobile
    ? ({
        paddingBottom: '80px', // Add space for bottom pill
      } as CSSProperties)
    : ({
        height: 'calc(100vh - 117px)',
        overflowY: 'auto',
      } as CSSProperties);

  return (
    <>
      <AppMetadata title={getPageTitle()} description={getPageDescription()} />
      <AppHeader />
      <TooltipProvider delayDuration={0}>
        <ResizablePanelGroup
          direction={isMobile ? 'vertical' : 'horizontal'}
          className='grow'
        >
          {!isMobile && (
            <ResizablePanel
              defaultSize={defaultLayout[0]}
              minSize={5}
              style={{
                overflow: isMobile ? 'visible !important' : 'hidden',
                display: isMobile ? 'block' : undefined,
                height: isMobile ? 100 : '100%',
              }}
            >
              {!isMobile && (
                <div className={cn('py-2 px-2 flex gap-2 items-center')}>
                  <AppAuthorSelect
                    value={authorId}
                    onChange={onAuthorSelectChange}
                  />
                </div>
              )}

              <Separator />

              <SearchTags
                style={{ height: 'calc(100vh - 143px)', overflowY: 'auto' }}
              />
            </ResizablePanel>
          )}
          {!isMobile && <ResizableHandle withHandle />}

          <ResizablePanel
            defaultSize={defaultLayout[1]}
            minSize={30}
            style={isMobile ? { overflowY: 'scroll' } : {}}
          >
            <Tabs
              defaultValue={resourceType}
              onValueChange={(x) => {
                setCurrentTab(x);
                if (x == 'proposal') return;
                onResourceTypeChange(x as AppResourceType);
              }}
              className='flex flex-col'
            >
              <div className='flex items-center px-4 py-2 flex-wrap gap-2'>
                {isAuthorized && !isMobile && (
                  <BookmarksToggle
                    isActive={bookmarksMode}
                    onToggle={onBookmarkSwitchChange}
                  />
                )}

                {!isMobile && <GlobalViewSwitcher currentTab={currentTab} />}

                {!isMobile && (
                  <TabsList className='ml-auto flex-wrap h-full px-2'>
                    <TabsTrigger
                      value={AppResourceType.Article}
                      className='text-zinc-600 dark:text-zinc-200'
                    >
                      Articles
                    </TabsTrigger>
                    <TabsTrigger
                      value={AppResourceType.Video}
                      className='text-zinc-600 dark:text-zinc-200'
                    >
                      Videos
                    </TabsTrigger>
                    <TabsTrigger
                      value={AppResourceType.Tweet}
                      className='text-zinc-600 dark:text-zinc-200'
                    >
                      Tweets
                    </TabsTrigger>
                    <TabsTrigger
                      value={AppResourceType.Github}
                      className='text-zinc-600 dark:text-zinc-200'
                    >
                      Github
                    </TabsTrigger>
                    <TabsTrigger
                      value={AppResourceType.Papers}
                      className='text-zinc-600 dark:text-zinc-200'
                    >
                      Papers
                    </TabsTrigger>
                    <TabsTrigger
                      value={AppResourceType.Sites}
                      className='text-zinc-600 dark:text-zinc-200'
                    >
                      Sites
                    </TabsTrigger>
                    {isAuthorized && (
                      <TabsTrigger
                        value='proposal'
                        className='text-zinc-600 dark:text-zinc-200'
                      >
                        My Proposals
                      </TabsTrigger>
                    )}
                  </TabsList>
                )}
              </div>
              <Separator />
              <TabsContent
                value={AppResourceType.Article}
                style={TabContentStyle}
              >
                <ArticleTab />
              </TabsContent>
              <TabsContent
                value={AppResourceType.Papers}
                style={TabContentStyle}
              >
                <ArticleTab />
              </TabsContent>
              <TabsContent
                value={AppResourceType.Tweet}
                style={TabContentStyle}
              >
                <TweetsTab />
              </TabsContent>

              <TabsContent
                value={AppResourceType.Github}
                style={TabContentStyle}
              >
                <GithubTab />
              </TabsContent>
              <TabsContent
                value={AppResourceType.Sites}
                style={TabContentStyle}
              >
                <SitesTab />
              </TabsContent>
              <TabsContent
                value={AppResourceType.Video}
                style={TabContentStyle}
              >
                <VideoTab />
              </TabsContent>
              {isAuthorized && (
                <TabsContent value='proposal' style={TabContentStyle}>
                  <ProposalsTab />
                </TabsContent>
              )}
            </Tabs>
          </ResizablePanel>
        </ResizablePanelGroup>
      </TooltipProvider>

      {/* Mobile Category Pill - only show on mobile */}
      {isMobile && (
        <MobileCategoryPill
          currentCategory={currentTab as AppResourceType | 'proposal'}
          onCategoryChange={(category) => {
            setCurrentTab(category);
            if (category !== 'proposal') {
              onResourceTypeChange(category as AppResourceType);
            }
          }}
        />
      )}
    </>
  );
}

export function SearchPage() {
  return (
    <GlobalViewProvider>
      <SearchPageContent />
    </GlobalViewProvider>
  );
}
